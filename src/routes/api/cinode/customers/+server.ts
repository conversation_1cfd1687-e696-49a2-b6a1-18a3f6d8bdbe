import { json } from "@sveltejs/kit"
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import type { CinodeCustomer } from "$lib/types/cinode"
import {
  CINODE_API_BASE,
  CINODE_APP_KEY,
  CINODE_COMPANY_ID,
} from "$env/static/private"

// Cinode API configuration
const CINODE_API_TOKEN = CINODE_APP_KEY
const COMPANY_ID = CINODE_COMPANY_ID

export const GET: RequestHandler = async () => {
  try {
    // Check if Cinode credentials are configured
    if (!CINODE_API_TOKEN || !COMPANY_ID) {
      console.warn(
        "Cinode API credentials not configured. Returning mock customers..."
      )

      // Return mock data for development
      const mockCustomers: CinodeCustomer[] = [
        {
          id: 1,
          companyId: parseInt(CINODE_COMPANY_ID || "0"),
          name: "Acme Corporation",
          description: "Technology consulting company",
          internalId: "ACME001",
          externalId: "EXT001",
          seoId: "acme-corporation",
          createdDateTime: "2025-01-01T00:00:00.000Z",
          updatedDateTime: "2025-01-01T00:00:00.000Z",
          lastTouchDateTime: "2025-01-01T00:00:00.000Z",
          links: [],
        },
        {
          id: 2,
          companyId: parseInt(CINODE_COMPANY_ID || "0"),
          name: "TechStart AB",
          description: "Startup technology company",
          internalId: "TECH001",
          externalId: "EXT002",
          seoId: "techstart-ab",
          createdDateTime: "2025-01-01T00:00:00.000Z",
          updatedDateTime: "2025-01-01T00:00:00.000Z",
          lastTouchDateTime: "2025-01-01T00:00:00.000Z",
          links: [],
        },
        {
          id: 3,
          companyId: parseInt(CINODE_COMPANY_ID || "0"),
          name: "Innovation Labs",
          description: "Research and development company",
          internalId: "INNO001",
          externalId: "EXT003",
          seoId: "innovation-labs",
          createdDateTime: "2025-01-01T00:00:00.000Z",
          updatedDateTime: "2025-01-01T00:00:00.000Z",
          lastTouchDateTime: "2025-01-01T00:00:00.000Z",
          links: [],
        },
      ]

      return json({
        success: true,
        customers: mockCustomers,
        message: "Mock customers returned (API credentials not configured)",
      })
    }

    // Make the API call to Cinode to fetch customers
    const response = await fetch(
      `${CINODE_API_BASE}/v0.1/companies/${COMPANY_ID}/customers`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${CINODE_API_TOKEN}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      console.error("Cinode API error:", response.status, errorText)

      return json(
        {
          error: `Cinode API error: ${response.status} ${response.statusText}`,
          details: errorText,
        },
        { status: response.status }
      )
    }

    const result = await response.json()

    return json({
      success: true,
      customers: result,
      message: "Customers fetched successfully from Cinode",
    })
  } catch (error) {
    console.error("Fetch customers error:", error)

    return json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
