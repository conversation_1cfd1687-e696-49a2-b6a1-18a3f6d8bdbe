// Types for Cinode API responses

export interface CinodeCustomer {
  id: number
  companyId: number
  name: string
  description: string
  internalId: string
  externalId: string
  seoId: string
  createdDateTime: string
  updatedDateTime: string
  lastTouchDateTime: string
  links: Array<{
    href: string
    rel: string
    methods: string[]
  }>
}

export interface CinodeCustomersResponse {
  success: boolean
  customers: CinodeCustomer[]
  message: string
  error?: string
}

export interface JobData {
  title: string
  description: string
  startDate?: string
  endDate?: string
  location?: string
  company?: string
  customerId?: number // Add customer ID for linking to selected customer
  rate?: string
  originalContent?: string
  fileName?: string
}
